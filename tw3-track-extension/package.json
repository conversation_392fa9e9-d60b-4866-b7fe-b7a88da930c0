{"name": "tw3-track-extension", "displayName": "Tw3 track extension", "version": "0.0.1", "description": "A basic Plasmo extension.", "author": "Plasmo Corp. <<EMAIL>>", "scripts": {"dev": "plasmo dev", "build": "plasmo build", "package": "plasmo package"}, "dependencies": {"@plasmohq/messaging": "^0.7.2", "@plasmohq/storage": "^1.15.0", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tooltip": "^1.2.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.523.0", "plasmo": "0.90.5", "react": "18.2.0", "react-dom": "18.2.0", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@ianvs/prettier-plugin-sort-imports": "4.1.1", "@shadcn/ui": "^0.0.4", "@types/chrome": "0.0.258", "@types/node": "20.11.5", "@types/react": "18.2.48", "@types/react-dom": "18.2.18", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "prettier": "3.2.4", "tailwindcss": "^3.4.0", "typescript": "5.3.3"}, "manifest": {"host_permissions": ["https://*.x.com/*", "https://*.twitter.com/*", "https://*.tw3track.com/*", "https://*.etherscan.io/*", "https://*.solscan.io/*", "https://*.suivision.xyz/*"], "permissions": ["alarms", "storage", "tabs", "sidePanel"], "web_accessible_resources": [{"matches": ["<all_urls>"], "resources": ["src/locales/*.json", "assets/*", "assets/**/*"]}], "side_panel": {"default_path": "sidepanel.html"}}}