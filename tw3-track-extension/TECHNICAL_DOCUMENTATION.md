# TW3 Track Extension - 技术文档

## 项目概述

**项目名称**: TW3 Track Extension  
**版本**: 0.0.1  
**描述**: 基于 Plasmo 框架开发的浏览器扩展，用于跟踪和管理 TW3 相关活动  
**开发框架**: Plasmo v0.90.5  

## 技术栈

### 核心框架
- **Plasmo**: 0.90.5 - 现代化的浏览器扩展开发框架
- **React**: 18.2.0 - 用户界面构建
- **TypeScript**: 5.3.3 - 类型安全的 JavaScript 超集

### UI 组件库
- **shadcn/ui**: 0.0.4 - 现代化的 React 组件库
- **Radix UI Slot**: 1.2.3 - 提供无障碍的底层组件
- **Tailwind CSS**: 3.4.0 - 实用优先的 CSS 框架
- **Lucide React**: 0.523.0 - 图标库

### 样式工具
- **class-variance-authority**: 0.7.1 - 组件变体管理
- **clsx**: 2.1.1 - 条件类名工具
- **tailwind-merge**: 3.3.1 - Tailwind 类名合并
- **PostCSS**: 8.5.6 - CSS 后处理器
- **Autoprefixer**: 10.4.21 - CSS 自动前缀

### Plasmo 生态
- **@plasmohq/messaging**: 0.7.2 - 扩展内消息传递
- **@plasmohq/storage**: 1.15.0 - 扩展存储管理

### 开发工具
- **Prettier**: 3.2.4 - 代码格式化
- **@ianvs/prettier-plugin-sort-imports**: 4.1.1 - 导入排序

## 项目架构

### 目录结构
tw3-track-extension/
├── .github/               # GitHub Actions 配置
│   └── workflows/
│       └── submit.yml     # 提交工作流
├── .gitignore             # Git 忽略文件
├── .prettierrc.mjs        # Prettier 配置
├── TECHNICAL_DOCUMENTATION.md # 技术文档
├── assets/                # 静态资源
│   ├── dex/              # DEX 相关图标
│   │   ├── axiom.ico     # Axiom 图标
│   │   ├── axiom.svg     # Axiom SVG 图标
│   │   ├── dexscreener.ico # DexScreener 图标
│   │   ├── geckoterminal.svg # GeckoTerminal 图标
│   │   ├── gmgn.ico      # GMGN 图标
│   │   ├── okx.webp      # OKX 图标
│   │   └── photon.svg    # Photon 图标
│   └── icon.png          # 扩展主图标
├── components.json        # shadcn/ui 配置
├── package.json           # 项目配置
├── pnpm-lock.yaml         # pnpm 锁定文件
├── postcss.config.js      # PostCSS 配置
├── src/                   # 源代码目录
│   ├── backgound/         # 后台脚本目录
│   ├── components/        # React 组件
│   │   └── ui/           # shadcn/ui 组件
│   │       └── button.tsx # 按钮组件
│   ├── content/           # 内容脚本目录
│   ├── content-scripts/   # 内容脚本相关
│   ├── lib/              # 工具函数库
│   │   └── utils.ts      # 通用工具函数
│   ├── locales/          # 国际化文件
│   │   ├── en.json       # 英文语言包
│   │   └── zh.json       # 中文语言包
│   ├── managers/         # 管理器模块
│   ├── popup.tsx         # 弹出窗口界面
│   ├── services/         # 服务层
│   ├── sidepanel/        # 侧边栏相关
│   │   └── index.tsx     # 侧边栏主界面
│   ├── styles/           # 样式文件
│   │   └── style.css     # 全局样式
│   ├── tabs/             # 标签页相关
│   ├── types/            # TypeScript 类型定义
│   └── utils/            # 工具函数
├── tailwind.config.js    # Tailwind CSS 配置
└── tsconfig.json         # TypeScript 配置
```

### 核心文件说明

#### 1. Manifest 配置
扩展具有以下权限和功能：

**主机权限**:
- `*.x.com/*` - Twitter/X 平台访问
- `*.twitter.com/*` - Twitter 平台访问
- `*.tw3track.com/*` - TW3 跟踪服务
- `*.etherscan.io/*` - 以太坊区块链浏览器
- `*.solscan.io/*` - Solana 区块链浏览器
- `*.suivision.xyz/*` - Sui 区块链浏览器

**扩展权限**:
- `alarms` - 定时任务
- `storage` - 本地存储
- `tabs` - 标签页管理
- `sidePanel` - 侧边栏功能

#### 2. 用户界面组件

**Popup 界面** (`src/popup.tsx`):
- 扩展的主要弹出窗口
- 包含输入框和多种按钮样式展示
- 使用 shadcn/ui 组件库
- 响应式设计，最小宽度 300px

**Side Panel 界面** (`src/sidepanel.tsx`):
- Chrome 侧边栏功能
- 提供跟踪和设置功能入口
- 简洁的用户界面设计

#### 3. 组件系统

**Button 组件** (`src/components/ui/button.tsx`):
- 基于 Radix UI Slot 构建
- 支持多种变体：default, destructive, outline, secondary, ghost, link
- 支持多种尺寸：default, sm, lg, icon
- 使用 class-variance-authority 管理样式变体

**工具函数** (`src/lib/utils.ts`):
- `cn()` 函数：结合 clsx 和 tailwind-merge 的类名处理工具
- 用于条件性应用和合并 Tailwind CSS 类名

## Plasmo 框架特性

### 1. 开发命令
```bash
# 开发模式 - 热重载开发
pnpm dev

# 构建生产版本
pnpm build

# 打包扩展
pnpm package
```

### 2. 文件路由系统
Plasmo 使用基于文件的路由系统：
- `popup.tsx` → 扩展弹出窗口
- `sidepanel.tsx` → Chrome 侧边栏
- `background.ts` → 后台脚本（如需要）
- `content.ts` → 内容脚本（如需要）

### 3. 自动化功能
- **热重载**: 开发时自动重载扩展
- **TypeScript 支持**: 内置 TypeScript 配置
- **Manifest 生成**: 自动生成 manifest.json
- **资源处理**: 自动处理静态资源

### 4. 扩展 API 集成
- **消息传递**: 使用 `@plasmohq/messaging` 进行组件间通信
- **存储管理**: 使用 `@plasmohq/storage` 管理扩展数据
- **Chrome APIs**: 完整的 Chrome 扩展 API 支持

## 设计系统

### 1. 颜色主题
项目使用基于 HSL 的设计令牌系统：

```css
/* 主要颜色 */
--primary: hsl(222.2 47.4% 11.2%);
--primary-foreground: hsl(210 40% 98%);

/* 次要颜色 */
--secondary: hsl(210 40% 96%);
--secondary-foreground: hsl(222.2 84% 4.9%);

/* 边框和输入 */
--border: hsl(214.3 31.8% 91.4%);
--input: hsl(214.3 31.8% 91.4%);

/* 背景 */
--background: hsl(0 0% 100%);
--foreground: hsl(222.2 84% 4.9%);
```

### 2. 圆角系统
```css
--radius-lg: 0.5rem;
--radius-md: calc(0.5rem - 2px);
--radius-sm: calc(0.5rem - 4px);
```

### 3. 组件变体
按钮组件支持以下变体：
- **default**: 主要操作按钮
- **outline**: 次要操作按钮
- **secondary**: 辅助操作按钮
- **ghost**: 透明背景按钮
- **link**: 链接样式按钮
- **destructive**: 危险操作按钮

## 开发指南

### 1. 环境设置
```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev

# 在浏览器中加载扩展
# Chrome: chrome://extensions/ -> 开发者模式 -> 加载已解压的扩展程序
```

### 2. 添加新组件
```bash
# 使用 shadcn/ui CLI 添加组件
npx shadcn-ui@latest add [component-name]
```

### 3. 代码规范
- 使用 TypeScript 进行类型安全开发
- 遵循 Prettier 代码格式化规则
- 使用 ESLint 进行代码质量检查
- 组件使用 React Hooks 模式

### 4. 样式指南
- 优先使用 Tailwind CSS 实用类
- 使用 `cn()` 函数处理条件样式
- 遵循设计令牌系统
- 保持响应式设计原则

## 部署和分发

### 1. 构建生产版本
```bash
pnpm build
```

### 2. 打包扩展
```bash
pnpm package
```

### 3. 发布到商店
- **Chrome Web Store**: 使用生成的 `.zip` 文件
- **Firefox Add-ons**: 需要额外配置 manifest v2 兼容性
- **Edge Add-ons**: 与 Chrome 兼容

## 性能优化

### 1. 代码分割
- Plasmo 自动进行代码分割
- 按需加载组件和功能模块

### 2. 资源优化
- 使用 SVG 图标减少文件大小
- 压缩图片资源
- 最小化 CSS 和 JavaScript

### 3. 运行时优化
- 使用 React.memo 优化组件渲染
- 实现适当的状态管理
- 避免不必要的重新渲染

## 安全考虑

### 1. 权限最小化
- 仅请求必要的主机权限
- 限制 API 访问范围

### 2. 数据保护
- 使用 Plasmo Storage API 安全存储数据
- 避免在代码中硬编码敏感信息

### 3. 内容安全策略
- 遵循 Chrome 扩展 CSP 要求
- 避免使用 `eval()` 和内联脚本

## 故障排除

### 1. 常见问题
- **热重载失效**: 重启开发服务器
- **样式不生效**: 检查 Tailwind CSS 配置
- **组件导入错误**: 验证文件路径和导出

### 2. 调试工具
- Chrome DevTools 扩展调试
- React Developer Tools
- Plasmo 开发者控制台

### 3. 日志和监控
- 使用 `console.log` 进行开发调试
- 实现错误边界处理
- 监控扩展性能指标

## 未来规划

### 1. 功能扩展
- 添加更多区块链网络支持
- 实现数据可视化功能
- 集成更多 DeFi 协议

### 2. 技术升级
- 升级到最新版本的依赖
- 实现更好的状态管理
- 添加单元测试和集成测试

### 3. 用户体验
- 改进界面设计
- 添加多语言支持
- 实现主题切换功能

---

**文档版本**: 1.0  
**最后更新**: 2024年12月  
**维护者**: TW3 Track Extension Team