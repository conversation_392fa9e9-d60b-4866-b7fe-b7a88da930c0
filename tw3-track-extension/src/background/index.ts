import { REFRESH_CONFIG } from '../config/config';
import { ApiClient } from '../services/auth.api';

// 设置定期刷新 alarm（60秒间隔）
chrome.alarms.create('userStatusRefresh', {
  delayInMinutes: REFRESH_CONFIG.BACKGROUND_INTERVAL / 60000,
  periodInMinutes: REFRESH_CONFIG.BACKGROUND_INTERVAL / 60000
});

// 监听 alarm 事件
chrome.alarms.onAlarm.addListener(async (alarm) => {
  if (alarm.name === 'userStatusRefresh') {
    try {
      if (REFRESH_CONFIG.USER_STATUS.ENABLE_LOGGING) {
        console.log('[Background] 执行定期用户状态刷新');
      }
      await ApiClient.refreshUserStatus();
    } catch (error) {
      if (REFRESH_CONFIG.USER_STATUS.ENABLE_LOGGING) {
        console.error('[Background] 用户状态刷新失败:', error);
      }
    }
  }
});

// 扩展启动时立即执行一次刷新
chrome.runtime.onStartup.addListener(async () => {
  try {
    console.log('[Background] 扩展启动，执行初始用户状态刷新');
    await ApiClient.refreshUserStatus();
  } catch (error) {
    console.error('[Background] 启动时用户状态刷新失败:', error);
  }
});

// 扩展安装时执行一次刷新
chrome.runtime.onInstalled.addListener(async () => {
  try {
    console.log('[Background] 扩展安装，执行初始用户状态刷新');
    await ApiClient.refreshUserStatus();
  } catch (error) {
    console.error('[Background] 安装时用户状态刷新失败:', error);
  }
});