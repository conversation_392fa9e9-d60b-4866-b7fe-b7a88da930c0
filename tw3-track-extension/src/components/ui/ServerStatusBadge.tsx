import React, { useState, useEffect } from 'react';
import { Badge } from './badge';

import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from './tooltip';
import { RefreshCw, Wifi, WifiOff } from 'lucide-react';
import { API_CONFIG } from '~src/config/config';

interface ServerStatusBadgeProps {
  showText?: boolean;
  size?: 'sm' | 'default' | 'lg';
  className?: string;
}

type ServerStatus = 'checking' | 'online' | 'offline' | 'error';

function ServerStatusBadge({ showText = true, size = 'default', className }: ServerStatusBadgeProps) {
  const [status, setStatus] = useState<ServerStatus>('checking');
  const [lastChecked, setLastChecked] = useState<Date | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // 检查服务器状态
  const checkServerStatus = async () => {
    setIsRefreshing(true);
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);

      const response = await fetch(`${API_CONFIG.BASE}${API_CONFIG.ENDPOINTS.HEALTH_CHECK}`, {
        method: 'GET',
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      clearTimeout(timeoutId);

      // 任何非502状态码都认为服务器在线
      if (response.status !== 502) {
        setStatus('online');
      } else {
        setStatus('offline');
      }
      
      setLastChecked(new Date());
    } catch (error) {
      console.error('Server status check failed:', error);
      if (error instanceof Error && error.name === 'AbortError') {
        setStatus('offline');
      } else {
        setStatus('error');
      }
      setLastChecked(new Date());
    } finally {
      setIsRefreshing(false);
    }
  };

  // 初始检查和定时检查
  useEffect(() => {
    checkServerStatus();
    
    // 每30秒检查一次
    const interval = setInterval(checkServerStatus, 30000);
    
    return () => clearInterval(interval);
  }, []);

  // 获取状态显示信息
  const getStatusInfo = () => {
    switch (status) {
      case 'checking':
        return {
          variant: 'secondary' as const,
          icon: <RefreshCw className="w-3 h-3 animate-spin" />,
          text: 'Checking...',
          color: 'text-blue-600'
        };
      case 'online':
        return {
          variant: 'default' as const,
          icon: <Wifi className="w-3 h-3" />,
          text: 'Online',
          color: 'text-green-600'
        };
      case 'offline':
        return {
          variant: 'destructive' as const,
          icon: <WifiOff className="w-3 h-3" />,
          text: 'Offline',
          color: 'text-red-600'
        };
      case 'error':
        return {
          variant: 'destructive' as const,
          icon: <WifiOff className="w-3 h-3" />,
          text: 'Error',
          color: 'text-red-600'
        };
    }
  };

  const statusInfo = getStatusInfo();

  const badgeContent = (
    <Badge 
      variant={statusInfo.variant}
      className={`cursor-pointer transition-all hover:scale-105 ${className}`}
      onClick={checkServerStatus}
    >
      <div className="flex items-center space-x-1">
        {isRefreshing ? (
          <RefreshCw className="w-3 h-3 animate-spin" />
        ) : (
          statusInfo.icon
        )}
        {showText && <span className="text-xs">{statusInfo.text}</span>}
      </div>
    </Badge>
  );

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          {badgeContent}
        </TooltipTrigger>
        <TooltipContent>
          <div className="text-xs">
            <div className="font-medium">Server Status: {statusInfo.text}</div>
            {lastChecked && (
              <div className="text-muted-foreground">
                Last checked: {lastChecked.toLocaleTimeString()}
              </div>
            )}
            <div className="text-muted-foreground mt-1">
              Click to refresh
            </div>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

export default ServerStatusBadge; 