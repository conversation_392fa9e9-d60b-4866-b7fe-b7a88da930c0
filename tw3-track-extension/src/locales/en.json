{"common": {"next": "Next", "previous": "Previous", "cancel": "Cancel", "confirm": "Confirm", "save": "Save", "close": "Close", "loading": "Loading...", "logout": "Logout", "refresh": "Refresh", "hour": "Hour", "minute": "Minute", "page": "Page", "clear": "Clear", "edit": "Edit", "delete": "Delete", "clearAll": "Clear All", "done": "Done", "saving": "Saving...", "deleting": "Deleting..."}, "userinfo": {"used": "Used", "twitterNotes": "Twitter Notes", "walletNotes": "Wallet Notes", "followTrends": "Follow Trends", "deletionReminder": "Deletion Reminder", "nextResetDate": "Next Reset Date", "expiryDate": "Expiry Date"}, "popup": {"welcome": "Welcome to the Tw3Track!", "notLoggedIn": "Extension is not logged in, please login first", "openLoginPage": "Open Login Page", "openDashboard": "Open Dashboard", "userInfo": "User Info", "kolSettings": "KOL Display Settings", "showKolList": "Show KOL List", "showKolListDesc": "<PERSON><PERSON><PERSON> followed KOL list on Twitter pages", "twitterSettings": "Twitter Settings", "autoQueryTitle": "Auto Query Twitter Data", "autoQueryDesc": "Automatically display data when visiting user profiles", "showKolListTitle": "Show KOL List", "showKolListShortDesc": "Display followed KOL list"}, "login": {"serverStatusCheck": "Server Status Check", "selectLanguage": "Select Language", "enterToken": "<PERSON>ter <PERSON>", "tokenPlaceholder": "Please enter your Authorization Token", "verifying": "Verifying...", "verificationSuccess": "Verification successful!", "selectTheme": "Select Theme Mode", "themeAuto": "Auto", "themeDark": "Dark Mode", "themeLight": "Light Mode", "termsTitle": "Terms of Use", "termsContent": "Please read the following terms of use carefully. By using this extension, you agree to comply with the relevant terms.", "agreeTerms": "I have read and agree to the terms of use", "setupComplete": "Setup Complete", "setupCompleteMessage": "Congratulations! You have successfully configured the extension and can now start using it.", "tokenEmpty": "Token cannot be empty", "tokenTooShort": "<PERSON><PERSON> appears to be too short", "tokenDescription": "Enter your authorization token to access the extension features", "authorizationToken": "Authorization Token", "tokenHelp": "Please enter your API token. You can find this in your account settings.", "pasteFromClipboard": "Paste from clipboard", "tokenSecurityInfo": "Your token will be securely stored locally and used to authenticate API requests. The token format should be: Bearer [your-token-here]", "exampleFormat": "Example format:", "tokenVerification": "Token Verification", "verifyingDescription": "Verifying your authorization token and retrieving account information", "verifyingWait": "Please wait while we verify your token...", "verificationSuccessDescription": "Your token has been verified successfully!", "verificationFailed": "Verification Failed", "verificationFailedDescription": "There was an issue verifying your token", "accountInformation": "Account Information", "accountDescription": "Your account details and subscription information", "userId": "User ID", "plan": "Plan", "usage": "Usage", "error": "Error", "languageDescription": "Choose your preferred language for the extension interface", "languageHelp": "Select your preferred language. The interface will update immediately.", "selectLanguagePlaceholder": "Select a language", "preview": "Preview", "themeDescription": "Choose your preferred theme mode for the extension interface", "themeMode": "Theme Mode", "themeHelp": "Select your preferred theme. The interface will update immediately to show the preview.", "themeLightDesc": "Always use light mode with bright colors and high contrast", "themeDarkDesc": "Always use dark mode with dark colors and reduced eye strain", "themeAutoDesc": "Automatically switch between light and dark based on system preference", "currentThemePreview": "Current theme preview:", "background": "Background", "primaryColor": "Primary color", "mutedElements": "Muted elements", "termsDescription": "Please read and accept the terms of use to continue", "termsAndPrivacy": "Terms of Use & Privacy Policy", "termsReadCarefully": "Please read the following terms carefully before proceeding", "dataPrivacySecurity": "Data Privacy & Security", "privacyPrinciples": "Your privacy and data security are our top priorities. This extension operates with the following principles:", "dataStoredLocally": "All data is stored locally on your device", "tokensEncrypted": "API tokens are encrypted and never shared with third parties", "noPersonalInfo": "No personal information is collected without consent", "canDeleteData": "You can delete all data at any time through settings", "usageGuidelines": "Usage Guidelines", "byUsingExtension": "By using this extension, you agree to:", "termsAcknowledge": "By checking this box, you acknowledge that you have read, understood, and agree to be bound by these terms.", "configurationSummary": "Configuration Summary", "configuredSettings": "Your extension has been configured with the following settings", "language": "Language", "theme": "Theme", "account": "Account", "terms": "Terms", "connected": "Connected", "accepted": "Accepted", "pending": "Pending", "accountOverview": "Account Overview", "accountReady": "Your account is ready to use", "status": "Status", "active": "Active", "whatsNext": "What's Next?", "nextStep": "Next Step", "getStarted": "Get Started", "thankYou": "Thank you for choosing our extension! 🎉", "clickGetStarted": "Click \"Get Started\" to close this setup and return to the extension", "accessDashboard": "Access the dashboard through the extension popup", "exploreFeatures": "Explore Web3 trends, manage notes, and customize settings", "changePreferences": "You can change language and theme preferences anytime in settings", "preloadingData": "Preloading Data", "preloadingDesc": "Caching your data in the background for better experience...", "progress": "Progress", "twitterData": "Twitter Notes Data ", "walletData": "Wallet Notes Data", "dataCacheRecommendation": "Recommended Data Caching", "dataCacheDesc": "For the best user experience, we recommend caching your data now", "cacheAdvantage1": "Data ready on first access without waiting", "cacheAdvantage2": "View cached data even when offline", "cacheAdvantage3": "Reduce API calls and improve response speed", "startDataCache": "Start Data Caching", "skipCache": "<PERSON><PERSON>", "cachingData": "Caching Data", "cachingDataDesc": "Caching your Twitter and wallet data, please wait...", "cachingComplete": "Caching Complete", "cachingCompleteDesc": "Data caching is complete, you can now start next step!", "cachingProgress": "Caching Progress", "cachingProgressDesc": "Fetching and caching your data from the server", "overallProgress": "Overall Progress", "statusPending": "Pending", "statusLoading": "Loading", "statusSuccess": "Complete", "statusError": "Failed", "cachingFailed": "Caching failed", "cachingSuccessTitle": "Caching Successful!", "cachingSuccessDesc": "All data has been successfully cached locally", "cachingInProgress": "Caching data in progress, please wait...", "cachingNote": "Caching process may take a few minutes depending on your data volume", "complianceWithLaws": "Use the extension in compliance with all applicable laws and regulations", "noReverseEngineering": "Not attempt to reverse engineer or modify the extension", "respectRateLimits": "Respect API rate limits and usage quotas", "noMaliciousUse": "Not use the extension for any malicious or harmful activities", "keepTokensSecure": "Keep your API tokens secure and not share them with others", "importantNotes": "Important Notes", "apiUsage": "API Usage", "apiUsageDesc": "This extension connects to external APIs to provide its functionality. Please ensure you have proper authorization to use these APIs and comply with their terms of service.", "dataBackup": "Data Backup", "dataBackupDesc": "While we store data locally for security, we recommend backing up important configurations. The extension provides export/import functionality in settings.", "contactSupport": "Contact & Support", "contactSupportDesc": "If you have any questions about these terms or need support, please contact us through:", "officialSupportChannels": "Official support channels:https://t.me/Tw3Track", "lastUpdated": "Last updated", "version": "Version", "expiryDate": "Expiry Date", "nextReset": "Query Next Reset", "retry": "Retry", "buttonPreview": "Button Preview", "primaryButton": "Primary", "outlineButton": "Outline", "ghostButton": "Ghost", "scrollToRead": "Please scroll down to read the complete terms", "readProgress": "read", "readComplete": "You have read the complete terms and conditions", "pleaseScrollFirst": "Please scroll down to read the complete terms first", "readTermsFirst": "Read Terms First", "acceptTerms": "Accept Terms", "completeSetup": "Complete Setup", "twitterSettings": "Twitter Settings", "twitterSettingsDescription": "Configure how the extension interacts with Twitter/X pages", "twitterAutoQuery": "Auto Query on Twitter", "twitterAutoQueryDescription": "Automatically display KOL data when visiting Twitter user profiles", "enableAutoQuery": "Enable Auto Query", "autoQueryExplanation": "When enabled, the extension will automatically show relevant data on Twitter user profiles", "recommended": "Recommended", "whatHappensNext": "What happens next:", "featureDescription1": "Visit any Twitter user profile to see KOL data automatically", "featureDescription2": "Data is cached for 10 seconds to improve performance", "featureDescription3": "You can toggle this feature anytime in the popup settings", "dexPlatformSettings": "DEX Platform Settings", "dexPlatformSettingsDescription": "Choose default DEX platforms for each network for quick trading access", "platformsAvailable": "platforms", "evmNetworkDesc": "Including Ethereum, Base, BSC and other networks", "solanaNetworkDesc": "Solana ecosystem", "suiNetworkDesc": "Sui blockchain network", "defaultDexPlatform": "Default DEX Platform", "selectDexPlatform": "Select DEX Platform", "selectedPlatform": "Selected", "dexPlatform": "DEX Platform", "defaultChain": "Default Chain", "selectChain": "Select Chain", "currentSettings": "Current Settings", "dexSettingsNote": "Settings Note", "dexSettingsNote1": "EVM networks will show multiple platform options, other networks use default platform", "dexSettingsNote2": "You can modify these preferences anytime in settings", "dexSettingsNote3": "These settings will be used for wallet address detection and blockchain explorer injection"}, "serverCheck": {"checking": "Checking Server Status", "online": "Server Online", "offline": "Server Offline", "error": "Connection Error", "unknown": "Unknown Status", "checkingDescription": "Verifying server connectivity and availability", "onlineDescription": "Server is online and ready to accept requests", "offlineDescription": "Server is currently unavailable or not responding", "errorDescription": "Unable to connect to the server", "errorDetails": "<PERSON><PERSON><PERSON>", "pleaseWait": "Please wait while we check the server status...", "connectionEstablished": "Connection established successfully", "retry": "Retry Connection", "retrying": "Retrying...", "skipCheck": "Skip Check & Continue", "troubleshootingTips": "Troubleshooting Tips", "tip1": "Check your internet connection", "tip2": "Verify the server is running and accessible", "tip3": "Contact support if the problem persists", "status": "Status", "server": "Server", "lastChecked": "Last Checked", "clickToRefresh": "Click to refresh"}, "twitterDisplay": {"title": "X Web3 Analysis", "loading": "Loading user data...", "error": "Failed to load data", "retry": "Retry", "notLoggedIn": "Please login to view Web3 analysis data", "loginPrompt": "Click the extension icon to login", "userInfo": "User Information", "kolFollowing": "KOL Following", "followers": "followers", "noData": "No data available", "autoQuery": "Auto Query Twitter Data", "autoQueryDesc": "Automatically fetch and display user data when visiting Twitter profiles", "cacheExpired": "Data cache expired, refreshing...", "serverError": "Server error, please try again later", "networkError": "Network error, please check your connection", "kolCount": "Following {count} KOLs", "showingTop": "Showing top {count} by followers", "lastUpdated": "Last updated: {time}", "refreshData": "Refresh Data", "poweredBy": "Powered by Tw3Track", "dataLoaded": "Data loaded successfully", "noKolData": "No KOL data available", "followerCount": "Followers", "followingCount": "Following", "userStats": "User Stats", "kolAnalysis": "KOL Analysis", "topKols": "Top KOLs", "loadingAnalysis": "Loading analysis...", "analysisComplete": "Analysis complete", "refresh": "Refresh", "nameChanges": "Name Changes", "screenNameChanges": "Username Changes", "followEvents": "Follow Events", "walletAddresses": "Wallet Addresses", "pumpTokens": "<PERSON><PERSON>kens", "raydiumTokens": "Raydium Tokens", "successRate": "Success Rate", "viewDetails": "View Details", "kolList": "KOL List", "showKolList": "Show KOL List", "showKolListDesc": "Display KOL following list in a separate card", "kolCard": "KOL Following ({count})", "tokenAnalysis": "Token Analysis", "socialStats": "Social Stats", "clickToView": "Click to view", "noHistory": "No history", "walletCount": "{count} wallets", "userNotFound": "No data found for this user", "poweredByBadge": "Tw3Track Extension", "chainDataDisclaimer": "*This data is parsed from blockchain, does not represent the person", "walletDisclaimer": "*These addresses cannot be 100% confirmed as user's wallets, for reference only", "userHistory": "Profile History", "userHistoryTooltip": "View profile change timeline", "memberFeature": "Member Feature", "upgradeMember": "Upgrade to Member", "upgradeDesc": "This is a premium feature. Please upgrade to member to access profile history timeline.", "profileChanges": "Profile Changes", "noHistoryData": "No history data available", "historyTimeline": "History Timeline", "collapse": "Collapse", "expand": "Expand"}, "sidePanel": {"title": "Twitter Data Details", "nameChangesTitle": "Name Change History", "screenNameChangesTitle": "Username Change History", "walletAddressesTitle": "Wallet Addresses", "kolListTitle": "KOL Following List", "noData": "No data available", "followers": "Followers", "following": "Following", "profile": "Profile", "overview": "Overview", "details": "Details", "refresh": "Refresh", "close": "Close", "copy": "Copy", "loading": "Loading...", "error": "Failed to load", "showingTopItems": "Showing top {count}", "totalItems": "Total {count}", "collapse": "Collapse", "expand": "Expand"}, "dashboard": {"title": "Tw3Track Dashboard", "subtitle": "X Web3 Trends", "web3Trends": "X Web3 Trends", "web3TrendsDesc": "Monitor and analyze X Web3 market trends", "earliestFollower": "Earliest Follower", "showAllFollowers": "Show All Followers", "withinMinutes": "within minutes", "withinHours": "within hours", "paused": "Paused", "autoRefresh": "Auto Refreshing", "noTrendsData": "No trends data available", "allFollowersOf": "All Followers of", "someAccountsHidden": "Some accounts are not public, so they cannot be displayed", "twitterNotes": "Twitter Notes Management", "twitterNotesDesc": "Manage notes for important Twitter posts", "walletNotes": "Wallet Notes Management", "walletNotesDesc": "Track and analyze wallet addresses", "settings": "Settings", "settingsDesc": "Configure your preferences and account", "searchTrends": "Search trends, tags, or sources...", "deletionReminder": "Deletion Reminder", "newFollowers": "new followers", "gotoProfile": "Go to profile"}, "userStatus": {"userId": "User ID", "plan": "Plan", "limit": "Limit", "used": "Used", "expiryDate": "Expiry Date", "nextResetDate": "Query Next Reset Date (UTC)", "twitterNotes": "Twitter Notes", "walletNotes": "Wallet Notes", "followTrends": "Follow Trends", "accountStatus": "Account Status", "maxTwitterAccounts": "Max Twitter Accounts", "maxWalletAddresses": "Max Wallet Addresses", "deletionReminder": "Deletion Reminder"}, "logout": {"confirmTitle": "Confirm <PERSON>ut", "confirmDescription": "Are you sure you want to logout? You will need to enter your token again to access the extension.", "confirmButton": "Logout", "loggingOut": "Logging out...", "success": "Logged out successfully"}, "walletNotes": {"title": "Wallet Notes Management", "desc": "Track and analyze wallet addresses", "add": "Add Wallet", "edit": "Edit", "delete": "Delete", "address": "Wallet Address", "network": "Network", "note": "Note", "source": "Source", "createdAt": "Added", "actions": "Actions", "explorer": "Explorer", "editNote": "Edit Note/Source", "editNoteDesc": "Only note and source can be edited. Wallet address and network are read-only.", "empty": "No wallet notes yet", "errorLoad": "Failed to load wallet notes. Please try again.", "errorAdd": "Failed to add. Please check your input.", "errorEdit": "Failed to edit. Please try again.", "errorDelete": "Failed to delete. Please try again.", "deleteConfirmTitle": "Confirm Delete", "deleteConfirmDesc": "Are you sure you want to delete this wallet note? This action cannot be undone.", "searchPlaceholder": "Search wallet address, network, note, or source...", "totalWallets": "Total Wallets", "networks": "Networks", "deleteSuccessDesc": "Wallet address deleted successfully.", "refreshSuccessDesc": "Wallet addresses refreshed.", "refreshSuccessTitle": "Refreshed", "deleteSuccessTitle": "Delete Success", "refreshConfirmDesc": "Refreshing will re-fetch all wallet addresses, which may take a while. Continue?", "refreshConfirmTitle": "Confirm Refresh", "confirmRefresh": "Confirm Refresh", "addWalletNote": "Add Wallet Note", "editWalletNote": "Edit Wallet Note", "walletAddress": "Wallet Address", "networkType": "Network Type", "noteContent": "Note Content", "noteContentPlaceholder": "Enter wallet note content...", "sourcePlaceholder": "Note source (optional)", "tags": "Tags", "selectTags": "Select Tags", "noTags": "No tags", "save": "Save", "cancel": "Cancel", "saving": "Saving...", "saveSuccess": "Note saved successfully", "saveError": "Save failed", "deleteNote": "Delete Note", "deleteConfirm": "Are you sure you want to delete this note?", "deleteSuccess": "Note deleted successfully", "deleteError": "Delete failed"}, "twitterNotes": {"title": "Twitter Notes Management", "desc": "Manage notes for important Twitter posts", "avatar": "Avatar", "name": "Name", "screenName": "Twitter ID", "note": "Note", "tags": "Tags", "createdAt": "Created", "actions": "Actions", "editNote": "Edit Note/Tags", "editNoteDesc": "You can edit the note and tags. Tags are limited to the fixed options.", "tagSelectLabel": "Select Tags", "tagLimitError": "You cannot select more than 13 tags", "noteEmptyError": "Note cannot be empty", "deleteConfirmTitle": "Confirm Delete", "deleteConfirmDesc": "This action cannot be undone. Continue?", "searchPlaceholder": "Search name/ID/tags/note...", "empty": "No Twitter notes yet", "errorLoad": "Failed to load Twitter notes. Please try again.", "errorEdit": "Failed to edit. Please try again.", "errorDelete": "Failed to delete. Please try again.", "refreshSuccessTitle": "Refreshed", "refreshSuccessDesc": "Twitter notes refreshed.", "deleteSuccessTitle": "Delete Success", "deleteSuccessDesc": "Twitter note deleted successfully.", "tagOptions": ["CryptoNews", "Airdrop", "Web3Dev", "VC", "CryptoOG", "OnChainData", "CryptoEducation", "Project", "MemeCoin", "GameFi", "NFT Collector", "CryptoRegulation", "MEV", "OG", "KOL"], "tagFilter": "<PERSON> Filter", "tagFilterLabel": "<PERSON> Filter", "totalCount": "Total {count}", "filteredCount": "Filtered {count}", "totalLabel": "Total", "filteredLabel": "Filtered", "itemsUnit": "items", "refreshConfirmTitle": "Confirm Data Refresh", "refreshConfirmDesc": "Refreshing will fetch the latest Twitter notes data from the server. This may take some time, are you sure you want to continue?", "confirmRefresh": "Confirm Refresh", "selectTagsPlaceholder": "Select tags...", "selectTags": "Select Tags", "selectedTags": "Selected tags:", "selectFilterTags": "Select tags to filter", "totalRecords": "Total {count} records", "enterNotePlaceholder": "Enter note content...", "tryAdjustSearch": "Try adjusting search conditions or refresh data", "openTwitter": "Open Twitter", "addNote": "Add Note", "updateNote": "Update Note", "deleteNote": "Delete Note", "resetNote": "Reset", "noteContent": "Note Content", "noteContentPlaceholder": "Enter note content...", "tagsLabel": "Tags", "tagsSelected": "selected", "saving": "Saving...", "deleting": "Deleting...", "clickToEdit": "Click to edit note", "clickToAdd": "Click to add note", "badgeTooltip": "Click to add or edit note", "noteAdded": "Note added successfully", "noteUpdated": "Note updated successfully", "noteDeleted": "Note deleted successfully", "deleteConfirm": "Are you sure you want to delete this note?", "noRestId": "Click the note button next to Twitter username to edit notes"}, "settings": {"title": "Settings", "language": "Language", "languageZh": "中文", "languageEn": "English", "theme": "Theme", "themeSystem": "Follow System", "themeLight": "Light", "themeDark": "Dark", "autoRefreshInterval": "Trends Auto Refresh Interval", "seconds": "seconds", "trendsCount": "Trends Display Count", "refreshTwitterNotes": "Refresh Twitter Notes", "refreshWalletNotes": "Refresh Wallet Notes", "refreshTwitterNotesDesc": "Re-fetch the latest Twitter notes data", "refreshWalletNotesDesc": "Re-fetch the latest wallet notes data", "dataManagementTip": "Data will be automatically synced to local cache for faster access", "refreshTip": "Recommended when data is abnormal or latest information is needed", "cacheStatus": "Cache status is good", "dataExport": "Data Export", "dataExportDesc": "Export local cached data to CSV format files", "exportTwitterNotes": "Export Twitter Notes", "exportWalletNotes": "Export Wallet Notes", "exportTwitterNotesDesc": "Export all Twitter notes data", "exportWalletNotesDesc": "Export all wallet notes data", "exportSuccess": "Export Successful", "exportFailed": "Export Failed", "noDataToExport": "No data to export", "refreshDataFirst": "Please refresh data first", "twitterNotesExported": "Twitter notes data exported", "walletNotesExported": "Wallet notes data exported", "exportConfirmTitle": "Confirm Data Export", "exportTwitterConfirmDesc": "You are about to export all Twitter notes data to a CSV file", "exportWalletConfirmDesc": "You are about to export all wallet notes data to a CSV file", "exportFormat": "Export Format", "exportContent": "Export Content", "twitterDataFields": "Twitter ID, Name, Username, Note, Tags, Created At", "walletDataFields": "Wallet Address, Network, Note, Source, Created At", "confirmExport": "Confirm Export", "exporting": "Exporting...", "twitterCacheManagement": "Twitter Data Cache Management", "twitterCacheManagementDesc": "Manage Twitter data cache and clean up historical cache to optimize extension performance", "twitterCacheStats": "<PERSON><PERSON>", "totalCache": "Total Cache", "dataCache": "<PERSON>", "followChangesCache": "Follow Changes Cache", "userHistoryCache": "User History Cache", "cacheOverLimitTitle": "Cache Overflow Warning", "cacheOverLimitDesc": "To ensure normal extension operation, please clear historical cache. Too much cache may affect extension performance.", "clearAllTwitterCache": "Clear All Twitter Cache", "clearCacheConfirmTitle": "Confirm <PERSON>", "clearCacheConfirmDesc": "This operation will clear all Twitter data cache, including user data and follow change records. Data will need to be re-fetched after clearing.", "clearCacheItems": "<PERSON><PERSON>", "clearCacheWarning": "Data will need to be re-fetched after clearing", "confirmClearCache": "Confirm Clear", "clearingCache": "Clearing...", "clearCacheSuccess": "<PERSON><PERSON> Cleared Successfully", "clearCacheSuccessDesc": "All Twitter data cache has been cleared", "clearCacheFailed": "Failed to Clear Cache", "cache": {"title": "Cache Management", "items": "Items", "dataCache": "<PERSON>", "followChangesCache": "Follow Changes Cache", "userHistoryCache": "User History Cache", "clear": "<PERSON>ache", "cleared": "<PERSON><PERSON>ed", "clearError": "Failed to Clear Cache", "clearConfirmTitle": "Confirm <PERSON>", "clearConfirmDescription": "This operation will clear all Twitter data cache, including user data and follow change records. Data will need to be re-fetched after clearing."}, "dataManagement": {"title": "Data Management"}, "refreshTwitterNotesDescription": "Re-fetch the latest Twitter notes data", "refreshWalletNotesDescription": "Re-fetch the latest wallet notes data", "exportTwitterNotesDescription": "Export all Twitter notes data to CSV file", "exportWalletNotesDescription": "Export all wallet notes data to CSV file", "refreshTwitterSuccess": "Twitter notes refreshed successfully", "refreshTwitterError": "Twitter notes refresh failed", "refreshWalletSuccess": "Wallet notes refreshed successfully", "refreshWalletError": "Wallet notes refresh failed", "exportTwitterSuccess": "Twitter notes exported successfully", "exportTwitterError": "Twitter notes export failed", "exportWalletSuccess": "Wallet notes exported successfully", "exportWalletError": "Wallet notes export failed"}, "walletDetection": {"title": "Wallet Address Detection", "addressFound": "addresses", "copy": "Copy", "explorer": "Explorer", "addNote": "Add Note", "editNote": "Edit Note", "source": "Source", "collapse": "Collapse", "expand": "Expand", "more": "more", "walletAnalysis": "Wallet Address/CA Address Analysis", "hasNote": "Has Note", "note": "Note", "poweredBy": "Powered by Tw3Track", "copySuccess": "Copied to clipboard", "copyFailed": "Co<PERSON> failed", "explorerOpened": "Explorer opened", "noteEditorFailed": "Failed to open note editor"}, "dexSettings": {"title": "DEX Platform Settings", "description": "Select default DEX platform for each network", "defaultPlatform": "Default Platform", "selectPlatform": "Select Platform", "platformUpdated": "DEX platform settings updated", "evmPlatform": "EVM Platform", "evmDefaultChain": "EVM Default Chain", "solana": "Solana Platform", "currentSettings": "Current Settings", "sui": "Sui Platform"}, "walletNoteDialog": {"addNote": "Add Wallet Note", "editNote": "Edit Wallet Note", "notePlaceholder": "Enter wallet note...", "noteAdded": "Wallet note added", "noteUpdated": "Wallet note updated", "noteDeleted": "Wallet note deleted", "deleteNote": "Delete Note", "confirmDelete": "Are you sure you want to delete this note?"}}