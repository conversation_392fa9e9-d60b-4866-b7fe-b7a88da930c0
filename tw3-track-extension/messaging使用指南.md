# @plasmohq/messaging 使用指南

## 概述

`@plasmohq/messaging` 是 Plasmo 框架提供的消息传递库，用于在浏览器扩展的不同组件之间进行通信。虽然项目中已安装此依赖，但目前尚未在代码中使用。

## 主要用途

### 1. 扩展组件间通信
- **Popup ↔ Background**: 弹出窗口与后台脚本通信
- **Content Script ↔ Background**: 内容脚本与后台脚本通信
- **Side Panel ↔ Background**: 侧边栏与后台脚本通信
- **Tab ↔ Background**: 标签页与后台脚本通信

### 2. 数据传递场景
- 用户操作事件传递
- 状态同步
- 配置更新
- 数据获取请求
- 通知推送

## 实际应用场景

### 场景1: Popup 向 Background 发送跟踪请求
```typescript
// popup.tsx
import { sendToBackground } from "@plasmohq/messaging"

const startTracking = async () => {
  const response = await sendToBackground({
    name: "start-tracking",
    body: {
      url: window.location.href,
      timestamp: Date.now()
    }
  })
  console.log("Tracking started:", response)
}
```

### 场景2: Background 处理消息
```typescript
// background/messages/start-tracking.ts
import type { PlasmoMessaging } from "@plasmohq/messaging"

const handler: PlasmoMessaging.MessageHandler = async (req, res) => {
  const { url, timestamp } = req.body
  
  // 执行跟踪逻辑
  const trackingId = await initializeTracking(url, timestamp)
  
  res.send({
    success: true,
    trackingId
  })
}

export default handler
```

### 场景3: Content Script 监听页面变化
```typescript
// content.ts
import { sendToBackground } from "@plasmohq/messaging"

// 监听页面变化并通知后台
const observer = new MutationObserver(() => {
  sendToBackground({
    name: "page-changed",
    body: {
      url: window.location.href,
      title: document.title
    }
  })
})

observer.observe(document.body, { childList: true, subtree: true })
```

## 建议的实现方案

### 1. 创建消息类型定义
```typescript
// src/types/messages.ts
export interface TrackingMessage {
  name: "start-tracking" | "stop-tracking" | "get-status"
  body: {
    url?: string
    timestamp?: number
    trackingId?: string
  }
}

export interface PageMessage {
  name: "page-changed" | "page-loaded"
  body: {
    url: string
    title: string
    data?: any
  }
}
```

### 2. 创建消息处理器目录结构
```
src/
├── background/
│   └── messages/
│       ├── start-tracking.ts
│       ├── stop-tracking.ts
│       ├── get-status.ts
│       └── page-changed.ts
```

### 3. 在现有组件中集成

**优化 Popup 组件**:
- 添加与后台的通信功能
- 实时获取跟踪状态
- 发送用户操作指令

**优化 Side Panel 组件**:
- 显示实时跟踪数据
- 与后台同步状态
- 提供详细的控制选项

## 优势

1. **类型安全**: 完整的 TypeScript 支持
2. **简化API**: 比原生 Chrome API 更易用
3. **自动序列化**: 自动处理数据序列化/反序列化
4. **错误处理**: 内置错误处理机制
5. **开发体验**: 更好的开发者体验

## 下一步建议

1. **创建后台脚本**: 实现 `src/background/index.ts`
2. **定义消息接口**: 创建统一的消息类型定义
3. **实现消息处理器**: 为不同功能创建对应的消息处理器
4. **集成到现有组件**: 在 popup 和 sidepanel 中使用消息传递
5. **添加内容脚本**: 实现页面监听和数据收集功能

## 潜在风险

1. **性能影响**: 频繁的消息传递可能影响性能
2. **内存泄漏**: 未正确清理的监听器可能导致内存泄漏
3. **安全考虑**: 需要验证消息来源和内容
4. **调试复杂性**: 异步消息传递增加调试难度

## 总结

`@plasmohq/messaging` 是构建复杂浏览器扩展的重要工具，特别适合需要多组件协作的场景。建议根据项目需求逐步引入，从简单的 popup-background 通信开始，逐步扩展到更复杂的功能。