import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '~components/ui/card';
import { Label } from '~components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~components/ui/select';
import { Button } from '~components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter } from '~components/ui/dialog';
import { Badge } from '~components/ui/badge';
import { RefreshCw, Download, FileText, Trash2, AlertTriangle, Settings } from 'lucide-react';
import { useSettings } from '~contexts/SettingsContext';
import { useI18n } from '~hooks/useI18n';
import { DataService } from '~utils/dataService';
import { TwitterApiClient } from '~utils/twitter-api';
import { APP_CONFIG } from '~utils/config';
import { toast } from "sonner";
import { Storage } from '@plasmohq/storage';
import { 
  DEX_PLATFORMS, 
  getDexPlatformsForNetwork, 
  getDefaultDexPlatform, 
  getDefaultDexSettings,
  migrateDexSettings,
  EVM_PLATFORMS,
  EVM_CHAINS,
  getEvmPlatformById,
  getEvmChainById,
  type DexPlatformSettings
} from '~config/dexPlatforms';

const TRENDS_COUNT_OPTIONS = [10, 20, 30, 40, 50];
const AUTO_REFRESH_OPTIONS = [5, 10, 15, 30, 60];

const storage = new Storage({
  area: 'local',
});

interface TwitterCacheStats {
  totalCount: number;
  dataCache: number;
  followChangesCache: number;
  userHistoryCache: number;
}

// DEX平台设置状态接口现在从配置文件导入

function SettingsModule() {
  const {
    language, setLanguage,
    theme, setTheme,
    autoRefreshInterval, setAutoRefreshInterval,
    trendsCount, setTrendsCount
  } = useSettings();

  const { t } = useI18n(language as 'zh' | 'en');

  // 数据管理相关状态
  const [twitterRefreshDialogOpen, setTwitterRefreshDialogOpen] = useState(false);
  const [walletRefreshDialogOpen, setWalletRefreshDialogOpen] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [exporting, setExporting] = useState(false);
  
  // 导出确认对话框状态
  const [exportTwitterDialogOpen, setExportTwitterDialogOpen] = useState(false);
  const [exportWalletDialogOpen, setExportWalletDialogOpen] = useState(false);

  // 推特缓存管理状态
  const [twitterCacheStats, setTwitterCacheStats] = useState<TwitterCacheStats>({
    totalCount: 0,
    dataCache: 0,
    followChangesCache: 0,
    userHistoryCache: 0
  });
  const [clearCacheDialogOpen, setClearCacheDialogOpen] = useState(false);
  const [clearingCache, setClearingCache] = useState(false);

  // DEX平台设置状态
  const [dexPlatformSettings, setDexPlatformSettings] = useState<DexPlatformSettings>(getDefaultDexSettings());
  const [savingDexSettings, setSavingDexSettings] = useState(false);

  // 获取推特缓存统计信息
  const getTwitterCacheStats = async (): Promise<TwitterCacheStats> => {
    try {
      const allKeys = await storage.getAll();
      const cacheKeys = Object.keys(allKeys);
      
      const dataCacheKeys = cacheKeys.filter(key => 
        key.startsWith(APP_CONFIG.STORAGE_KEYS.TWITTER_DATA_CACHE) && 
        !key.includes('_follow_changes_') &&
        !key.includes('_user_history_')
      );
      
      const followChangesCacheKeys = cacheKeys.filter(key => 
        key.startsWith(APP_CONFIG.STORAGE_KEYS.TWITTER_DATA_CACHE) && 
        key.includes('_follow_changes_')
      );

      const userHistoryCacheKeys = cacheKeys.filter(key => 
        key.startsWith(APP_CONFIG.STORAGE_KEYS.TWITTER_DATA_CACHE) && 
        key.includes('_user_history_')
      );

      const stats = {
        totalCount: dataCacheKeys.length + followChangesCacheKeys.length + userHistoryCacheKeys.length,
        dataCache: dataCacheKeys.length,
        followChangesCache: followChangesCacheKeys.length,
        userHistoryCache: userHistoryCacheKeys.length
      };

      return stats;
    } catch (error) {
      console.error('Error getting Twitter cache stats:', error);
      return { totalCount: 0, dataCache: 0, followChangesCache: 0, userHistoryCache: 0 };
    }
  };

  // 加载DEX平台设置
  const loadDexPlatformSettings = async () => {
    try {
      const savedSettings = await storage.get('dex_platform_settings');
      const migratedSettings = migrateDexSettings(savedSettings);
      setDexPlatformSettings(migratedSettings);
      
      // 如果设置被迁移了，保存新格式
      if (JSON.stringify(savedSettings) !== JSON.stringify(migratedSettings)) {
        await storage.set('dex_platform_settings', migratedSettings);
        console.log('✅ DEX设置已迁移到新格式');
      }
    } catch (error) {
      console.error('Failed to load DEX platform settings:', error);
      setDexPlatformSettings(getDefaultDexSettings());
    }
  };

  // 保存DEX平台设置
  const saveDexPlatformSettings = async (newSettings: DexPlatformSettings) => {
    setSavingDexSettings(true);
    try {
      await storage.set('dex_platform_settings', newSettings);
      setDexPlatformSettings(newSettings);
      toast.success(t('dexSettings.platformUpdated'));
    } catch (error) {
      console.error('Failed to save DEX platform settings:', error);
      toast.error('保存失败，请重试');
    } finally {
      setSavingDexSettings(false);
    }
  };

  // 更新单个网络的DEX平台
  const updateDexPlatform = async (network: keyof DexPlatformSettings, platformId: string) => {
    let newSettings: DexPlatformSettings;
    
    if (network === 'evm') {
      // EVM网络不应该通过这个函数更新，使用专门的函数
      console.error('Use updateEvmPlatform for EVM settings');
      return;
    } else {
      newSettings = { ...dexPlatformSettings, [network]: platformId };
    }
    
    await saveDexPlatformSettings(newSettings);
  };

  // 更新EVM平台设置
  const updateEvmPlatform = async (platform: string) => {
    const newSettings = {
      ...dexPlatformSettings,
      evm: {
        ...dexPlatformSettings.evm,
        platform
      }
    };
    await saveDexPlatformSettings(newSettings);
  };

  // 更新EVM默认链设置
  const updateEvmChain = async (defaultChain: string) => {
    const newSettings = {
      ...dexPlatformSettings,
      evm: {
        ...dexPlatformSettings.evm,
        defaultChain
      }
    };
    await saveDexPlatformSettings(newSettings);
  };

  // 更新缓存统计信息
  const updateCacheStats = async () => {
    const stats = await getTwitterCacheStats();
    setTwitterCacheStats(stats);
  };

  // 组件挂载时获取缓存统计和DEX设置
  useEffect(() => {
    updateCacheStats();
    loadDexPlatformSettings();
  }, []);

  // 清除所有推特数据缓存
  const clearAllTwitterCache = async () => {
    setClearingCache(true);
    try {
      await TwitterApiClient.clearAllCache();
      await updateCacheStats();
      toast.success(t('settings.clearCacheSuccess'), {
        description: t('settings.clearCacheSuccessDesc')
      });
    } catch (error) {
      toast.error(t('settings.clearCacheFailed'), {
        description: 'Failed to clear Twitter cache'
      });
    } finally {
      setClearingCache(false);
      setClearCacheDialogOpen(false);
    }
  };

  // 刷新推特备注
  const refreshTwitterNotes = async () => {
    setRefreshing(true);
    try {
      const result = await DataService.fetchTwitterNotes();
      
      if (result.success) {
        if (result.cached) {
          toast.warning('Rate limit exceeded', {
            description: 'Using cached data. Please try again later'
          });
        } else {
          toast.success(t('twitterNotes.refreshSuccessTitle'), {
            description: t('twitterNotes.refreshSuccessDesc')
          });
        }
      } else {
        toast.error('Refresh failed', {
          description: result.error || 'Failed to refresh Twitter notes'
        });
      }
    } catch (error) {
      toast.error('Refresh failed', {
        description: 'Failed to refresh Twitter notes'
      });
    } finally {
      setRefreshing(false);
      setTwitterRefreshDialogOpen(false);
    }
  };

  // 刷新钱包备注
  const refreshWalletNotes = async () => {
    setRefreshing(true);
    try {
      const result = await DataService.fetchWalletNotes();
      
      if (result.success) {
        if (result.cached) {
          toast.warning('Rate limit exceeded', {
            description: 'Using cached data. Please try again later'
          });
        } else {
          toast.success(t('walletNotes.refreshSuccessTitle'), {
            description: t('walletNotes.refreshSuccessDesc')
          });
        }
      } else {
        toast.error('Refresh failed', {
          description: result.error || 'Failed to refresh wallet notes'
        });
      }
    } catch (error) {
      toast.error('Refresh failed', {
        description: 'Failed to refresh wallet notes'
      });
    } finally {
      setRefreshing(false);
      setWalletRefreshDialogOpen(false);
    }
  };

  // 检查并显示导出推特数据确认对话框
  const handleExportTwitterNotes = async () => {
    const data = await DataService.getLocalTwitterNotes();
    if (data.length === 0) {
      toast.warning(t('settings.noDataToExport'), {
        description: t('settings.refreshDataFirst')
      });
      return;
    }
    setExportTwitterDialogOpen(true);
  };

  // 确认导出推特数据
  const confirmExportTwitterNotes = async () => {
    setExporting(true);
    try {
              await DataService.exportTwitterNotesToCSV();
      toast.success(t('settings.exportSuccess'), {
        description: t('settings.twitterNotesExported')
      });
    } catch (error) {
      toast.error(t('settings.exportFailed'), {
        description: 'Failed to export Twitter notes'
      });
    } finally {
      setExporting(false);
      setExportTwitterDialogOpen(false);
    }
  };

  // 检查并显示导出钱包数据确认对话框
  const handleExportWalletNotes = async () => {
    const data = await DataService.getLocalWalletNotes();
    if (data.length === 0) {
      toast.warning(t('settings.noDataToExport'), {
        description: t('settings.refreshDataFirst')
      });
      return;
    }
    setExportWalletDialogOpen(true);
  };

  // 确认导出钱包数据
  const confirmExportWalletNotes = async () => {
    setExporting(true);
    try {
              await DataService.exportWalletNotesToCSV();
      toast.success(t('settings.exportSuccess'), {
        description: t('settings.walletNotesExported')
      });
    } catch (error) {
      toast.error(t('settings.exportFailed'), {
        description: 'Failed to export wallet notes'
      });
    } finally {
      setExporting(false);
      setExportWalletDialogOpen(false);
    }
  };

  // 判断缓存是否过多
  const isCacheOverLimit = twitterCacheStats.totalCount > 500;

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>{t('settings.title')}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <Label>{t('settings.language')}</Label>
            <Select value={language} onValueChange={setLanguage}>
              <SelectTrigger><SelectValue /></SelectTrigger>
              <SelectContent>
                <SelectItem value="zh">{t('settings.languageZh')}</SelectItem>
                <SelectItem value="en">{t('settings.languageEn')}</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label>{t('settings.theme')}</Label>
            <Select value={theme} onValueChange={setTheme}>
              <SelectTrigger><SelectValue /></SelectTrigger>
              <SelectContent>
                <SelectItem value="system">{t('settings.themeSystem')}</SelectItem>
                <SelectItem value="light">{t('settings.themeLight')}</SelectItem>
                <SelectItem value="dark">{t('settings.themeDark')}</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label>{t('settings.autoRefreshInterval')}</Label>
            <Select value={autoRefreshInterval.toString()} onValueChange={v => setAutoRefreshInterval(Number(v))}>
              <SelectTrigger><SelectValue /></SelectTrigger>
              <SelectContent>
                {AUTO_REFRESH_OPTIONS.map(n => (
                  <SelectItem key={n} value={n.toString()}>{n} {t('settings.seconds')}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label>{t('settings.trendsCount')}</Label>
            <Select value={trendsCount.toString()} onValueChange={v => setTrendsCount(Number(v))}>
              <SelectTrigger><SelectValue /></SelectTrigger>
              <SelectContent>
                {TRENDS_COUNT_OPTIONS.map(n => (
                  <SelectItem key={n} value={n.toString()}>{n}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* DEX平台设置卡片 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="w-5 h-5" />
            {t('dexSettings.title')}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-sm text-muted-foreground">{t('dexSettings.description')}</p>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {/* EVM Networks (Ethereum, Base, BSC) */}
            <div className="space-y-3 col-span-full">
              <Label className="text-sm font-medium">EVM Networks</Label>
              <p className="text-xs text-muted-foreground">选择平台和默认链 (Ethereum, Base, BSC)</p>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                {/* 平台选择 */}
                <div className="space-y-2">
                  <Label className="text-xs font-medium text-muted-foreground">DEX平台</Label>
                  <Select 
                    value={dexPlatformSettings.evm.platform} 
                    onValueChange={updateEvmPlatform}
                    disabled={savingDexSettings}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择平台" />
                    </SelectTrigger>
                    <SelectContent>
                      {EVM_PLATFORMS.map(platform => (
                        <SelectItem key={platform.id} value={platform.id}>
                          <div className="flex items-center gap-2">
                            <img 
                              src={platform.icon} 
                              className="w-4 h-4" 
                              alt={platform.name}
                              onError={(e) => (e.currentTarget.style.display = 'none')}
                            />
                            {platform.name}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* 链选择 */}
                <div className="space-y-2">
                  <Label className="text-xs font-medium text-muted-foreground">默认链</Label>
                  <Select 
                    value={dexPlatformSettings.evm.defaultChain} 
                    onValueChange={updateEvmChain}
                    disabled={savingDexSettings}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择链" />
                    </SelectTrigger>
                    <SelectContent>
                      {EVM_CHAINS.map(chain => (
                        <SelectItem key={chain.id} value={chain.id}>
                          {chain.displayName}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* 当前选择显示 */}
              <div className="text-xs text-muted-foreground bg-muted/30 p-2 rounded border">
                当前设置: {getEvmPlatformById(dexPlatformSettings.evm.platform)?.name || '未知平台'} - {getEvmChainById(dexPlatformSettings.evm.defaultChain)?.displayName || '未知链'}
              </div>
            </div>

            {/* Solana */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Solana</Label>
              <Select 
                value={dexPlatformSettings.solana} 
                onValueChange={(value) => updateDexPlatform('solana', value)}
                disabled={savingDexSettings}
              >
                <SelectTrigger>
                  <SelectValue placeholder={t('dexSettings.selectPlatform')} />
                </SelectTrigger>
                <SelectContent>
                  {getDexPlatformsForNetwork('solana').map(platform => (
                    <SelectItem key={platform.id} value={platform.id}>
                      <div className="flex items-center gap-2">
                        <img 
                          src={platform.icon} 
                          className="w-4 h-4" 
                          alt={platform.name}
                          onError={(e) => (e.currentTarget.style.display = 'none')}
                        />
                        {platform.name}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Sui */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Sui</Label>
              <Select 
                value={dexPlatformSettings.sui} 
                onValueChange={(value) => updateDexPlatform('sui', value)}
                disabled={savingDexSettings}
              >
                <SelectTrigger>
                  <SelectValue placeholder={t('dexSettings.selectPlatform')} />
                </SelectTrigger>
                <SelectContent>
                  {getDexPlatformsForNetwork('sui').map(platform => (
                    <SelectItem key={platform.id} value={platform.id}>
                      <div className="flex items-center gap-2">
                        <img 
                          src={platform.icon} 
                          className="w-4 h-4" 
                          alt={platform.name}
                          onError={(e) => (e.currentTarget.style.display = 'none')}
                        />
                        {platform.name}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 数据管理卡片 */}
      <Card>
        <CardHeader>
          <CardTitle>{t('settings.dataManagement')}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* 数据刷新部分 */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label className="text-sm font-medium">{t('settings.refreshTwitterNotes')}</Label>
                <p className="text-sm text-muted-foreground">{t('settings.refreshTwitterNotesDesc')}</p>
              </div>
              <Button 
                onClick={() => setTwitterRefreshDialogOpen(true)}
                disabled={refreshing || exporting || clearingCache}
                className="gap-2"
              >
                <RefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
                {t('common.refresh')}
              </Button>
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <Label className="text-sm font-medium">{t('settings.refreshWalletNotes')}</Label>
                <p className="text-sm text-muted-foreground">{t('settings.refreshWalletNotesDesc')}</p>
                <span className="flex items-center gap-1 text-xs text-green-600 mt-1">
                  <span className="h-2 w-2 bg-green-500 rounded-full animate-pulse"></span>
                  {t('settings.cacheStatus')}
                </span>
              </div>
              <Button 
                onClick={() => setWalletRefreshDialogOpen(true)}
                disabled={refreshing || exporting || clearingCache}
                className="gap-2"
              >
                <RefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
                {t('common.refresh')}
              </Button>
            </div>
          </div>

          {/* 分隔线 */}
          <div className="border-t"></div>

          {/* 推特数据缓存管理部分 */}
          <div className="space-y-4">
            <div>
              <Label className="text-sm font-medium">{t('settings.twitterCacheManagement')}</Label>
              <p className="text-sm text-muted-foreground">{t('settings.twitterCacheManagementDesc')}</p>
            </div>
            
            <div className="p-4 border rounded-lg bg-muted/30">
              <div className="space-y-3">
                                 <div className="flex items-center justify-between">
                   <span className="text-sm font-medium">{t('settings.twitterCacheStats')}</span>
                   <Button 
                     onClick={updateCacheStats}
                     size="sm"
                     variant="ghost"
                     className="h-6 px-2"
                   >
                     <RefreshCw className="w-3 h-3" />
                   </Button>
                 </div>
                
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 text-xs">
                  <div className="flex items-center justify-between p-2 bg-background rounded border">
                    <span className="text-muted-foreground">{t('settings.totalCache')}</span>
                    <Badge variant={isCacheOverLimit ? "destructive" : "secondary"} className="text-xs">
                      {twitterCacheStats.totalCount}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between p-2 bg-background rounded border">
                    <span className="text-muted-foreground">{t('settings.dataCache')}</span>
                    <Badge variant="outline" className="text-xs">
                      {twitterCacheStats.dataCache}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between p-2 bg-background rounded border">
                    <span className="text-muted-foreground">{t('settings.followChangesCache')}</span>
                    <Badge variant="outline" className="text-xs">
                      {twitterCacheStats.followChangesCache}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between p-2 bg-background rounded border">
                    <span className="text-muted-foreground">{t('settings.userHistoryCache')}</span>
                    <Badge variant="outline" className="text-xs">
                      {twitterCacheStats.userHistoryCache}
                    </Badge>
                  </div>
                </div>

                {isCacheOverLimit && (
                  <div className="flex items-start gap-2 p-3 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded-lg">
                    <AlertTriangle className="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0" />
                    <div className="text-xs text-red-700 dark:text-red-400">
                      <p className="font-medium">{t('settings.cacheOverLimitTitle')}</p>
                      <p>{t('settings.cacheOverLimitDesc')}</p>
                    </div>
                  </div>
                )}

                <div className="flex justify-end">
                  <Button 
                    onClick={() => setClearCacheDialogOpen(true)}
                    disabled={refreshing || exporting || clearingCache}
                    size="sm"
                    variant={isCacheOverLimit ? "destructive" : "outline"}
                    className="gap-2"
                  >
                    <Trash2 className="w-3 h-3" />
                    {t('settings.clearAllTwitterCache')}
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* 分隔线 */}
          <div className="border-t"></div>

          {/* 数据导出部分 */}
          <div className="space-y-4">
            <div>
              <Label className="text-sm font-medium">{t('settings.dataExport')}</Label>
              <p className="text-sm text-muted-foreground">{t('settings.dataExportDesc')}</p>
            </div>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <div className="text-sm font-medium">{t('settings.exportTwitterNotes')}</div>
                  <div className="text-xs text-muted-foreground">{t('settings.exportTwitterNotesDesc')}</div>
                </div>
                <Button 
                  onClick={handleExportTwitterNotes}
                  disabled={refreshing || exporting || clearingCache}
                  size="sm"
                  variant="outline"
                  className="gap-2 ml-2"
                >
                  <Download className="w-4 h-4" />
                  CSV
                </Button>
              </div>
              
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <div className="text-sm font-medium">{t('settings.exportWalletNotes')}</div>
                  <div className="text-xs text-muted-foreground">{t('settings.exportWalletNotesDesc')}</div>
                </div>
                <Button 
                  onClick={handleExportWalletNotes}
                  disabled={refreshing || exporting || clearingCache}
                  size="sm"
                  variant="outline"
                  className="gap-2 ml-2"
                >
                  <Download className="w-4 h-4" />
                  CSV
                </Button>
              </div>
            </div>
          </div>
          
          {/* 数据管理提示信息 */}
          <div className="mt-4 p-3 bg-muted/50 rounded-lg border-l-4 border-green-500">
            <span className="flex items-center gap-2 text-sm text-muted-foreground">
              <span className="h-2 w-2 bg-green-500 rounded-full animate-pulse"></span>
              {t('settings.dataManagementTip')}
            </span>
            <span className="flex items-center gap-2 text-xs text-muted-foreground mt-1 ml-4">
              {t('settings.refreshTip')}
            </span>
          </div>
        </CardContent>
      </Card>

      {/* 推特备注刷新确认弹窗 */}
      <Dialog open={twitterRefreshDialogOpen} onOpenChange={setTwitterRefreshDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('twitterNotes.refreshConfirmTitle')}</DialogTitle>
            <DialogDescription>
              {t('twitterNotes.refreshConfirmDesc')}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setTwitterRefreshDialogOpen(false)} disabled={refreshing}>
              {t('common.cancel')}
            </Button>
            <Button onClick={refreshTwitterNotes} disabled={refreshing}>
              {refreshing ? t('common.loading') : t('twitterNotes.confirmRefresh')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 钱包备注刷新确认弹窗 */}
      <Dialog open={walletRefreshDialogOpen} onOpenChange={setWalletRefreshDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('walletNotes.refreshConfirmTitle')}</DialogTitle>
            <DialogDescription>
              {t('walletNotes.refreshConfirmDesc')}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setWalletRefreshDialogOpen(false)} disabled={refreshing}>
              {t('common.cancel')}
            </Button>
            <Button onClick={refreshWalletNotes} disabled={refreshing}>
              {refreshing ? t('common.loading') : t('walletNotes.confirmRefresh')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 清除推特缓存确认弹窗 */}
      <Dialog open={clearCacheDialogOpen} onOpenChange={setClearCacheDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('settings.clearCacheConfirmTitle')}</DialogTitle>
            <DialogDescription>
              {t('settings.clearCacheConfirmDesc')}
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Trash2 className="w-4 h-4 text-muted-foreground" />
                <span className="text-sm">{t('settings.clearCacheItems')}: {twitterCacheStats.totalCount}</span>
              </div>
              <div className="flex items-center space-x-2">
                <FileText className="w-4 h-4 text-muted-foreground" />
                <span className="text-sm">{t('settings.clearCacheWarning')}</span>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setClearCacheDialogOpen(false)} disabled={clearingCache}>
              {t('common.cancel')}
            </Button>
            <Button onClick={clearAllTwitterCache} disabled={clearingCache} variant="destructive">
              {clearingCache ? t('settings.clearingCache') : t('settings.confirmClearCache')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 推特数据导出确认弹窗 */}
      <Dialog open={exportTwitterDialogOpen} onOpenChange={setExportTwitterDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('settings.exportConfirmTitle')}</DialogTitle>
            <DialogDescription>
              {t('settings.exportTwitterConfirmDesc')}
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <FileText className="w-4 h-4 text-muted-foreground" />
                <span className="text-sm">{t('settings.exportFormat')}: CSV</span>
              </div>
              <div className="flex items-center space-x-2">
                <Download className="w-4 h-4 text-muted-foreground" />
                <span className="text-sm">{t('settings.exportContent')}: {t('settings.twitterDataFields')}</span>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setExportTwitterDialogOpen(false)} disabled={exporting}>
              {t('common.cancel')}
            </Button>
            <Button onClick={confirmExportTwitterNotes} disabled={exporting}>
              {exporting ? t('settings.exporting') : t('settings.confirmExport')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 钱包数据导出确认弹窗 */}
      <Dialog open={exportWalletDialogOpen} onOpenChange={setExportWalletDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('settings.exportConfirmTitle')}</DialogTitle>
            <DialogDescription>
              {t('settings.exportWalletConfirmDesc')}
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <FileText className="w-4 h-4 text-muted-foreground" />
                <span className="text-sm">{t('settings.exportFormat')}: CSV</span>
              </div>
              <div className="flex items-center space-x-2">
                <Download className="w-4 h-4 text-muted-foreground" />
                <span className="text-sm">{t('settings.exportContent')}: {t('settings.walletDataFields')}</span>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setExportWalletDialogOpen(false)} disabled={exporting}>
              {t('common.cancel')}
            </Button>
            <Button onClick={confirmExportWalletNotes} disabled={exporting}>
              {exporting ? t('settings.exporting') : t('settings.confirmExport')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default SettingsModule; 